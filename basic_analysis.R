# ===============================================================================
# 英国消费者对可持续肉类的支付意愿研究 - 基础分析
# ===============================================================================

# 加载必要的包
library(readxl)
library(dplyr)
library(tidyr)
library(stringr)

cat("开始基础数据分析...\n")

# ===============================================================================
# 第一部分：数据读取和清理
# ===============================================================================

# 1. 读取数据
raw <- read_excel("data.xlsx", sheet = 1)
cat("原始数据维度:", dim(raw), "\n")

# 2. 处理标题行
title <- raw[1:2,]
raw <- raw[-c(1,2),]

# 3. 处理treatment变量
raw <- raw %>%
  rename(Q7 = starts_with("Q7. Cheap talk")) %>%
  mutate(Q7_trim = str_trim(Q7)) %>%
  mutate(treatment = case_when(
    is.na(Q7_trim) ~ NA_integer_,
    Q7_trim == "-" ~ 1L,  # Control group
    str_starts(Q7_trim, "Yes") ~ 2L,  # Treatment group (saw information)
    TRUE ~ 1L
  ))

cat("Treatment分布:\n")
treatment_table <- table(raw$treatment, useNA="ifany")
print(treatment_table)

# 4. 重命名选择题列
names(raw)[21:28] <- paste0("Q", 1:8, "_choice")

# ===============================================================================
# 第二部分：DCE数据分析
# ===============================================================================

# 1. 提取DCE数据
dce_raw <- raw %>%
  select(respondent_id = UserNo, treatment, Q1_choice:Q8_choice)

# 2. 转换为长格式
dce_long <- dce_raw %>%
  pivot_longer(
    cols = Q1_choice:Q8_choice,
    names_to = "qn",
    values_to = "chosen_Option"
  ) %>%
  mutate(task_id = as.integer(str_extract(qn, "\\d+"))) %>%
  select(respondent_id, task_id, chosen_Option, treatment)

# 3. 清理选择数据
dce_long <- dce_long %>%
  mutate(chosen_Option = str_extract(chosen_Option, "[ABC]")) %>%
  filter(!is.na(chosen_Option)) %>%
  select(respondent_id, task_id, chosen_Option, treatment)

cat("清洗后DCE数据维度:", dim(dce_long), "\n")

# ===============================================================================
# 第三部分：描述性统计
# ===============================================================================

cat("\n=== 描述性统计 ===\n")

# 1. 每人回答题目数量
task_counts <- dce_long %>%
  distinct(respondent_id, task_id) %>%
  count(respondent_id, name = "n_answered")

cat("每人回答题目数量分布:\n")
print(table(task_counts$n_answered))

# 2. 选择频率统计
choice_freq_overall <- dce_long %>%
  count(chosen_Option, name = "count") %>%
  mutate(percentage = count / sum(count) * 100)

cat("\n整体选择频率:\n")
print(choice_freq_overall)

# 3. 按treatment分组的选择频率
choice_freq_by_treatment <- dce_long %>%
  group_by(treatment, chosen_Option) %>%
  summarise(count = n(), .groups = "drop") %>%
  group_by(treatment) %>%
  mutate(percentage = count / sum(count) * 100)

cat("\n按treatment分组的选择频率:\n")
print(choice_freq_by_treatment)

# ===============================================================================
# 第四部分：创建表格
# ===============================================================================

cat("\n=== 创建分析表格 ===\n")

# 表1：基础描述统计
table1_desc <- data.frame(
  Statistic = c("总样本量", "Treatment 1 (Control)", "Treatment 2 (Information)", 
                "有效选择观测", "平均每人回答题目数"),
  Value = c(
    nrow(raw),
    sum(raw$treatment == 1, na.rm = TRUE),
    sum(raw$treatment == 2, na.rm = TRUE),
    nrow(dce_long),
    round(mean(task_counts$n_answered), 2)
  ),
  stringsAsFactors = FALSE
)

# 表2：选择频率对比
table2_choices <- choice_freq_by_treatment %>%
  pivot_wider(
    names_from = treatment,
    values_from = c(count, percentage),
    names_sep = "_"
  ) %>%
  mutate(
    Option_Name = case_when(
      chosen_Option == "A" ~ "Lab-grown meat",
      chosen_Option == "B" ~ "Plant-based meat",
      chosen_Option == "C" ~ "Conventional beef",
      TRUE ~ chosen_Option
    )
  ) %>%
  select(Option_Name, everything(), -chosen_Option)

# 表3：市场份额预测（基于选择频率）
table3_market_share <- choice_freq_by_treatment %>%
  select(treatment, chosen_Option, percentage) %>%
  pivot_wider(
    names_from = treatment,
    values_from = percentage,
    names_prefix = "Treatment_"
  ) %>%
  mutate(
    Product = case_when(
      chosen_Option == "A" ~ "Lab-grown meat",
      chosen_Option == "B" ~ "Plant-based meat", 
      chosen_Option == "C" ~ "Conventional beef",
      TRUE ~ chosen_Option
    ),
    Difference = Treatment_2 - Treatment_1
  ) %>%
  select(Product, Treatment_1, Treatment_2, Difference)

# ===============================================================================
# 第五部分：保存结果
# ===============================================================================

cat("\n=== 保存结果 ===\n")

# 保存表格
write.csv(table1_desc, "table1_descriptive_statistics.csv", row.names = FALSE)
write.csv(table2_choices, "table2_choice_frequencies.csv", row.names = FALSE)
write.csv(table3_market_share, "table3_market_shares_basic.csv", row.names = FALSE)
write.csv(choice_freq_by_treatment, "detailed_choice_frequencies.csv", row.names = FALSE)

# 显示主要结果
cat("\n=== 主要结果 ===\n")

cat("\n表1：描述性统计\n")
print(table1_desc)

cat("\n表2：选择频率对比\n")
print(table2_choices)

cat("\n表3：基础市场份额（基于选择频率）\n")
print(table3_market_share)

# ===============================================================================
# 第六部分：简单的偏好分析
# ===============================================================================

cat("\n=== 简单偏好分析 ===\n")

# 计算相对于传统牛肉的偏好
preference_analysis <- choice_freq_by_treatment %>%
  filter(chosen_Option != "C") %>%  # 排除传统牛肉
  group_by(treatment) %>%
  summarise(
    Alternative_Preference = sum(percentage),
    .groups = "drop"
  ) %>%
  mutate(
    Treatment_Name = case_when(
      treatment == 1 ~ "Control",
      treatment == 2 ~ "Information",
      TRUE ~ as.character(treatment)
    )
  )

cat("\n替代肉类总偏好（相对于传统牛肉）:\n")
print(preference_analysis)

# 信息提供的影响
if(nrow(preference_analysis) >= 2) {
  info_effect <- preference_analysis$Alternative_Preference[2] - 
                 preference_analysis$Alternative_Preference[1]
  cat("\n信息提供对替代肉类偏好的影响:", round(info_effect, 2), "个百分点\n")
}

# 保存偏好分析结果
write.csv(preference_analysis, "preference_analysis_basic.csv", row.names = FALSE)

cat("\n基础分析完成！\n")
cat("结果文件已保存:\n")
cat("- table1_descriptive_statistics.csv\n")
cat("- table2_choice_frequencies.csv\n") 
cat("- table3_market_shares_basic.csv\n")
cat("- detailed_choice_frequencies.csv\n")
cat("- preference_analysis_basic.csv\n")

cat("\n注意：这是基于选择频率的基础分析。\n")
cat("如需更精确的econometric模型分析，请使用mixed logit模型。\n")
