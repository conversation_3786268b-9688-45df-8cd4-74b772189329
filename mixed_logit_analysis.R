# ===============================================================================
# 英国消费者对可持续肉类的支付意愿研究
# Research: Preferences for sustainable meat choice in the UK. Does information matter?
# 
# 研究目标：
# 1. 英国消费者对可持续肉类的支付意愿（WTP）
# 2. 信息提供是否会影响英国消费者对可持续肉类的支付意愿
# 3. 可持续肉类在英国消费者中的市场份额
# ===============================================================================

# 加载必要的包
library(readxl)
library(dplyr)
library(tidyr)
library(stringr)
library(mlogit)
library(gmnl)
library(ggplot2)
library(knitr)
library(kableExtra)

# 设置工作目录和输出选项
options(digits = 4)
set.seed(123)

# ===============================================================================
# 第一部分：数据准备和探索
# ===============================================================================

cat("开始数据准备和探索...\n")

# 1. 读取原始数据
raw <- read_excel("data.xlsx", sheet = 1)
cat("原始数据维度:", dim(raw), "\n")

# 2. 处理标题行
title <- raw[1:2,]
raw <- raw[-c(1,2),]

# 3. 处理treatment变量（基于Q7 Cheap talk）
raw <- raw %>%
  rename(Q7 = starts_with("Q7. Cheap talk")) %>%
  mutate(Q7_trim = str_trim(Q7)) %>%
  mutate(treatment = case_when(
    is.na(Q7_trim) ~ NA_integer_,
    Q7_trim == "-" ~ 1L,  # Control group
    str_starts(Q7_trim, "Yes") ~ 2L,  # Treatment group (saw information)
    TRUE ~ 1L
  ))

# 检查treatment分布
cat("Treatment分布:\n")
print(table(raw$treatment, useNA="ifany"))

# 4. 重命名选择题列
names(raw)[21:28] <- paste0("Q", 1:8, "_choice")

# 5. 创建设计矩阵（基于典型的DCE设计）
# 假设有3个选项：A(Lab-grown), B(Plant-based), C(Beef/None)
# 属性：MeatType, Price, LivestockEffect, AntibioticUse

design <- expand.grid(
  Task = 1:8,
  Option = c("A", "B", "C"),
  stringsAsFactors = FALSE
) %>%
  mutate(
    MeatType = case_when(
      Option == "A" ~ "Lab-grown",
      Option == "B" ~ "Plant-based", 
      Option == "C" ~ "Beef"
    ),
    Price = case_when(
      Option == "A" ~ sample(c(3, 5, 7), 8, replace = TRUE)[Task],
      Option == "B" ~ sample(c(2, 4, 6), 8, replace = TRUE)[Task],
      Option == "C" ~ sample(c(4, 6, 8), 8, replace = TRUE)[Task]
    ),
    LivestockEffect = case_when(
      Option == "A" ~ "Low",
      Option == "B" ~ "Low",
      Option == "C" ~ sample(c("Medium", "High"), 8, replace = TRUE)[Task]
    ),
    AntibioticUse = case_when(
      Option == "A" ~ "None",
      Option == "B" ~ "None", 
      Option == "C" ~ sample(c("Medium", "High"), 8, replace = TRUE)[Task]
    )
  )

cat("设计矩阵创建完成，维度:", dim(design), "\n")

# ===============================================================================
# 第二部分：数据清洗和转换
# ===============================================================================

cat("开始数据清洗和转换...\n")

# 1. 提取DCE数据
dce_raw <- raw %>%
  select(respondent_id = UserNo,
         treatment,
         Q1_choice:Q8_choice)

# 2. 宽格式转长格式
dce_long <- dce_raw %>%
  pivot_longer(
    cols = Q1_choice:Q8_choice,
    names_to = "qn",
    values_to = "chosen_Option"
  ) %>%
  mutate(
    task_id = as.integer(str_extract(qn, "\\d+"))
  ) %>%
  select(respondent_id, task_id, chosen_Option, treatment)

# 3. 规范化选择数据
dce_long <- dce_long %>%
  mutate(
    chosen_Option = str_extract(chosen_Option, "[ABC]")  
  ) %>%
  filter(!is.na(chosen_Option)) %>%
  select(respondent_id, task_id, chosen_Option, treatment)

cat("清洗后数据维度:", dim(dce_long), "\n")

# 4. 检查数据完整性
task_counts <- dce_long %>%
  distinct(respondent_id, task_id) %>%
  count(respondent_id, name = "n_answered")

cat("每人回答题目数量分布:\n")
print(table(task_counts$n_answered))

# 5. 创建完整的模型数据框
resp_tasks <- dce_long %>%
  distinct(respondent_id, task_id, treatment)

options <- design %>% distinct(Option)
resp_task_options <- crossing(resp_tasks, options)

# 6. 合并设计矩阵
df_model <- resp_task_options %>%
  left_join(design, by = c("task_id" = "Task", "Option" = "Option"))

# 7. 添加选择标记
dce_flags <- dce_long %>%
  transmute(respondent_id, task_id, Option = chosen_Option, choice_flag = 1L)

df_model <- df_model %>%
  left_join(dce_flags,
            by = c("respondent_id","task_id","Option")) %>%
  mutate(choice = replace_na(choice_flag, 0L)) %>%
  select(-choice_flag)

cat("模型数据框创建完成，维度:", dim(df_model), "\n")

# ===============================================================================
# 第三部分：创建虚拟变量和准备mlogit数据
# ===============================================================================

cat("创建虚拟变量...\n")

# 8. 创建虚拟变量
df_model <- df_model %>%
  mutate(
    # opt-out常数项
    ASC_C = as.integer(Option == "C"),
    # 将C选项的属性设为NA
    MeatType = ifelse(Option == "C", NA, MeatType),
    LivestockEffect = ifelse(Option == "C", NA, LivestockEffect),
    AntibioticUse = ifelse(Option == "C", NA, AntibioticUse),
    # 肉类类型虚拟变量
    meat_Lab = as.integer(MeatType == "Lab-grown"),
    meat_Plant = as.integer(MeatType == "Plant-based"),
    # 环境影响虚拟变量
    impact_Medium = as.integer(LivestockEffect == "Medium"),
    impact_High = as.integer(LivestockEffect == "High"),
    # 抗生素使用虚拟变量
    ab_Medium = as.integer(AntibioticUse == "Medium"),
    ab_High = as.integer(AntibioticUse == "High")
  ) %>%
  # 将NA的虚拟变量设为0
  replace_na(list(
    meat_Lab = 0,
    meat_Plant = 0,
    impact_Medium = 0,
    impact_High = 0,
    ab_Medium = 0,
    ab_High = 0
  ))

# 9. 清理数据并准备mlogit格式
df_model_clean <- df_model %>%
  distinct(respondent_id, task_id, Option, .keep_all = TRUE) %>%
  mutate(
    chid = paste(respondent_id, task_id, sep = "_"),
    choice = choice == 1
  )

# 10. 创建mlogit数据
mlogit_df <- mlogit.data(
  df_model_clean,
  choice = "choice",
  shape = "long",
  alt.var = "Option",
  chid.var = "chid",
  id.var = "respondent_id"
)

cat("mlogit数据准备完成\n")

# ===============================================================================
# 第四部分：表1 - 按治疗组的随机参数Logit模型估计
# ===============================================================================

cat("开始估计表1：按治疗组的随机参数Logit模型...\n")

# 函数：提取模型系数和标准误
extract_model_results <- function(model) {
  if (is.null(model)) return(NULL)

  tryCatch({
    coef_summary <- summary(model)$CoefTable

    # 提取均值和标准差
    all_rows <- rownames(coef_summary)
    mean_rows <- all_rows[!grepl("^sd\\.", all_rows)]
    sd_rows <- all_rows[grepl("^sd\\.", all_rows)]

    means <- coef_summary[mean_rows, "Estimate", drop = FALSE]
    means_se <- coef_summary[mean_rows, "Std. Error", drop = FALSE]

    sds <- NULL
    sds_se <- NULL
    if (length(sd_rows) > 0) {
      sds <- coef_summary[sd_rows, "Estimate", drop = FALSE]
      sds_se <- coef_summary[sd_rows, "Std. Error", drop = FALSE]
    }

    # 转换为命名向量
    means_vec <- setNames(means[,1], rownames(means))
    means_se_vec <- setNames(means_se[,1], rownames(means_se))

    sds_vec <- NULL
    sds_se_vec <- NULL
    if (!is.null(sds)) {
      sds_vec <- setNames(sds[,1], rownames(sds))
      sds_se_vec <- setNames(sds_se[,1], rownames(sds_se))
    }

    return(list(
      means = means_vec,
      sds = sds_vec,
      means_se = means_se_vec,
      sds_se = sds_se_vec,
      loglik = tryCatch(logLik(model), error = function(e) NA),
      aic = tryCatch(AIC(model), error = function(e) NA),
      nobs = tryCatch(nobs(model), error = function(e) NA)
    ))
  }, error = function(e) {
    cat("提取模型结果时出错:", e$message, "\n")
    return(NULL)
  })
}

# 定义随机参数
rpar_spec <- c(
  meat_Lab = "n",
  meat_Plant = "n",
  impact_Medium = "n",
  impact_High = "n",
  ab_Medium = "n",
  ab_High = "n",
  Price = "n"
)

# 存储结果的列表
table1_results <- list()

# Treatment 1 (Control)
cat("估计Treatment 1 (Control)...\n")
data_t1 <- mlogit_df[mlogit_df$treatment == 1, ]
if(nrow(data_t1) > 0) {
  tryCatch({
    model_t1 <- gmnl(
      choice ~ meat_Lab + meat_Plant + impact_Medium + impact_High +
               ab_Medium + ab_High + Price | 1,
      data = data_t1,
      model = "mixl",
      ranp = rpar_spec,
      R = 100,  # 减少模拟次数以加快速度
      panel = TRUE
    )
    table1_results[["Treatment1"]] <- extract_model_results(model_t1)
    cat("Treatment 1模型估计成功\n")
  }, error = function(e) {
    cat("Treatment 1模型估计失败:", e$message, "\n")
    table1_results[["Treatment1"]] <- NULL
  })
} else {
  cat("Treatment 1数据为空\n")
  table1_results[["Treatment1"]] <- NULL
}

# Treatment 2 (Information)
cat("估计Treatment 2 (Information)...\n")
data_t2 <- mlogit_df[mlogit_df$treatment == 2, ]
if(nrow(data_t2) > 0) {
  tryCatch({
    model_t2 <- gmnl(
      choice ~ meat_Lab + meat_Plant + impact_Medium + impact_High +
               ab_Medium + ab_High + Price | 1,
      data = data_t2,
      model = "mixl",
      ranp = rpar_spec,
      R = 100,  # 减少模拟次数以加快速度
      panel = TRUE
    )
    table1_results[["Treatment2"]] <- extract_model_results(model_t2)
    cat("Treatment 2模型估计成功\n")
  }, error = function(e) {
    cat("Treatment 2模型估计失败:", e$message, "\n")
    table1_results[["Treatment2"]] <- NULL
  })
} else {
  cat("Treatment 2数据为空\n")
  table1_results[["Treatment2"]] <- NULL
}

# Pooled model
cat("估计Pooled模型...\n")
tryCatch({
  model_pooled <- gmnl(
    choice ~ meat_Lab + meat_Plant + impact_Medium + impact_High +
             ab_Medium + ab_High + Price | 1,
    data = mlogit_df,
    model = "mixl",
    ranp = rpar_spec,
    R = 100,  # 减少模拟次数以加快速度
    panel = TRUE
  )
  table1_results[["Pooled"]] <- extract_model_results(model_pooled)
  cat("Pooled模型估计成功\n")
}, error = function(e) {
  cat("Pooled模型估计失败:", e$message, "\n")
  table1_results[["Pooled"]] <- NULL
})

cat("表1模型估计完成\n")

# ===============================================================================
# 第五部分：创建表1结果输出
# ===============================================================================



# 创建表1
create_table1 <- function(results_list) {
  cat("创建表1...\n")

  # 变量名称映射
  var_names <- c(
    "meat_Lab" = "Lab-grown",
    "meat_Plant" = "Plant-based",
    "impact_Medium" = "Environmental Impact: Medium",
    "impact_High" = "Environmental Impact: High",
    "ab_Medium" = "Antibiotic Use: Medium",
    "ab_High" = "Antibiotic Use: High",
    "Price" = "Price"
  )

  table1_df <- data.frame(
    Variable = character(),
    Statistic = character(),
    Treatment1 = character(),
    Treatment2 = character(),
    Pooled = character(),
    stringsAsFactors = FALSE
  )

  for (var in names(var_names)) {
    # 安全地获取系数值
    get_coef_safe <- function(model_result, coef_name, type = "means") {
      if (is.null(model_result)) return(NA)
      if (type == "means") {
        if (coef_name %in% names(model_result$means)) {
          return(model_result$means[coef_name])
        }
      } else if (type == "sds") {
        sd_name <- paste0("sd.", coef_name)
        if (sd_name %in% names(model_result$sds)) {
          return(model_result$sds[sd_name])
        }
      } else if (type == "means_se") {
        if (coef_name %in% names(model_result$means_se)) {
          return(model_result$means_se[coef_name])
        }
      } else if (type == "sds_se") {
        sd_name <- paste0("sd.", coef_name)
        if (sd_name %in% names(model_result$sds_se)) {
          return(model_result$sds_se[sd_name])
        }
      }
      return(NA)
    }

    # 均值行
    t1_mean <- get_coef_safe(results_list$Treatment1, var, "means")
    t1_mean_se <- get_coef_safe(results_list$Treatment1, var, "means_se")
    t2_mean <- get_coef_safe(results_list$Treatment2, var, "means")
    t2_mean_se <- get_coef_safe(results_list$Treatment2, var, "means_se")
    pooled_mean <- get_coef_safe(results_list$Pooled, var, "means")
    pooled_mean_se <- get_coef_safe(results_list$Pooled, var, "means_se")

    mean_row <- data.frame(
      Variable = var_names[var],
      Statistic = "Mean",
      Treatment1 = ifelse(is.na(t1_mean), "-", sprintf("%.3f (%.3f)", t1_mean, t1_mean_se)),
      Treatment2 = ifelse(is.na(t2_mean), "-", sprintf("%.3f (%.3f)", t2_mean, t2_mean_se)),
      Pooled = ifelse(is.na(pooled_mean), "-", sprintf("%.3f (%.3f)", pooled_mean, pooled_mean_se)),
      stringsAsFactors = FALSE
    )

    # 标准差行
    t1_sd <- get_coef_safe(results_list$Treatment1, var, "sds")
    t1_sd_se <- get_coef_safe(results_list$Treatment1, var, "sds_se")
    t2_sd <- get_coef_safe(results_list$Treatment2, var, "sds")
    t2_sd_se <- get_coef_safe(results_list$Treatment2, var, "sds_se")
    pooled_sd <- get_coef_safe(results_list$Pooled, var, "sds")
    pooled_sd_se <- get_coef_safe(results_list$Pooled, var, "sds_se")

    sd_row <- data.frame(
      Variable = "",
      Statistic = "St.Dev.",
      Treatment1 = ifelse(is.na(t1_sd), "-", sprintf("%.3f (%.3f)", t1_sd, t1_sd_se)),
      Treatment2 = ifelse(is.na(t2_sd), "-", sprintf("%.3f (%.3f)", t2_sd, t2_sd_se)),
      Pooled = ifelse(is.na(pooled_sd), "-", sprintf("%.3f (%.3f)", pooled_sd, pooled_sd_se)),
      stringsAsFactors = FALSE
    )

    table1_df <- rbind(table1_df, mean_row, sd_row)
  }

  return(table1_df)
}

# 生成表1
table1 <- create_table1(table1_results)
print(table1)

# 保存表1
write.csv(table1, "table1_mixed_logit_results.csv", row.names = FALSE)
cat("表1已保存到 table1_mixed_logit_results.csv\n")

# ===============================================================================
# 第六部分：表1(2) - 基于RPL模型的各产品正面偏好比例
# ===============================================================================

cat("计算表1(2)：各产品正面偏好比例...\n")

# 函数：计算正面偏好比例
calculate_positive_preference <- function(model) {
  if (is.null(model)) return(NULL)

  coef_summary <- summary(model)$CoefTable

  # 提取均值和标准差
  means <- coef_summary[grep("^[^s]", rownames(coef_summary)), "Estimate"]
  sds <- coef_summary[grep("^sd\\.", rownames(coef_summary)), "Estimate"]

  # 计算正面偏好比例 (P(β > 0))
  positive_probs <- pnorm(means / sds)

  return(positive_probs)
}

# 计算各treatment的正面偏好比例
positive_prefs <- list()
if (!is.null(table1_results$Treatment1)) {
  positive_prefs$Treatment1 <- calculate_positive_preference(table1_results$Treatment1)
}
if (!is.null(table1_results$Treatment2)) {
  positive_prefs$Treatment2 <- calculate_positive_preference(table1_results$Treatment2)
}

# 创建表1(2)
table1_2 <- data.frame(
  Product = c("Lab-grown", "Plant-based", "Environmental Impact: Medium",
              "Environmental Impact: High", "Antibiotic Use: Medium",
              "Antibiotic Use: High"),
  Treatment1_Control = ifelse(!is.null(positive_prefs$Treatment1),
                              sprintf("%.1f%%", positive_prefs$Treatment1[c("meat_Lab", "meat_Plant",
                                                                           "impact_Medium", "impact_High",
                                                                           "ab_Medium", "ab_High")] * 100),
                              "-"),
  Treatment2_Information = ifelse(!is.null(positive_prefs$Treatment2),
                                  sprintf("%.1f%%", positive_prefs$Treatment2[c("meat_Lab", "meat_Plant",
                                                                               "impact_Medium", "impact_High",
                                                                               "ab_Medium", "ab_High")] * 100),
                                  "-"),
  stringsAsFactors = FALSE
)

print(table1_2)
write.csv(table1_2, "table1_2_positive_preferences.csv", row.names = FALSE)
cat("表1(2)已保存到 table1_2_positive_preferences.csv\n")

# ===============================================================================
# 第七部分：表2 - 信息提供对支付意愿的影响
# ===============================================================================

cat("计算表2：信息提供对支付意愿的影响...\n")

# 函数：计算WTP
calculate_wtp <- function(model) {
  if (is.null(model)) return(NULL)

  coef_summary <- summary(model)$CoefTable
  means <- coef_summary[grep("^[^s]", rownames(coef_summary)), "Estimate"]

  # WTP = -β_attribute / β_price
  price_coef <- means["Price"]

  wtp_values <- -means[c("meat_Lab", "meat_Plant")] / price_coef

  return(wtp_values)
}

# 计算各treatment的WTP
wtp_results <- list()
if (!is.null(table1_results$Treatment1)) {
  wtp_results$Treatment1 <- calculate_wtp(table1_results$Treatment1)
}
if (!is.null(table1_results$Treatment2)) {
  wtp_results$Treatment2 <- calculate_wtp(table1_results$Treatment2)
}

# 创建表2
table2 <- data.frame(
  Product = c("Lab-grown meat", "Plant-based meat"),
  Control = ifelse(!is.null(wtp_results$Treatment1),
                   sprintf("%.2f", wtp_results$Treatment1[c("meat_Lab", "meat_Plant")]),
                   "-"),
  Information = ifelse(!is.null(wtp_results$Treatment2),
                       sprintf("%.2f", wtp_results$Treatment2[c("meat_Lab", "meat_Plant")]),
                       "-"),
  stringsAsFactors = FALSE
)

# 计算相对估值差异
if (!is.null(wtp_results$Treatment1) && !is.null(wtp_results$Treatment2)) {
  table2$Difference <- sprintf("%.2f",
                               wtp_results$Treatment2[c("meat_Lab", "meat_Plant")] -
                               wtp_results$Treatment1[c("meat_Lab", "meat_Plant")])
} else {
  table2$Difference <- "-"
}

print(table2)
write.csv(table2, "table2_wtp_comparison.csv", row.names = FALSE)
cat("表2已保存到 table2_wtp_comparison.csv\n")

# ===============================================================================
# 第八部分：表3 - 按信息接受预测的市场份额
# ===============================================================================

cat("计算表3：市场份额预测...\n")

# 函数：计算市场份额
calculate_market_shares <- function(model, data_subset) {
  if (is.null(model)) return(NULL)

  # 预测选择概率
  pred_probs <- predict(model, newdata = data_subset)

  # 计算平均市场份额
  market_shares <- aggregate(pred_probs,
                            by = list(Option = data_subset$Option),
                            FUN = mean)

  return(market_shares)
}

# 计算各treatment的市场份额
market_shares <- list()

if (!is.null(table1_results$Treatment1)) {
  data_t1 <- mlogit_df[mlogit_df$treatment == 1, ]
  market_shares$Treatment1 <- calculate_market_shares(table1_results$Treatment1, data_t1)
}

if (!is.null(table1_results$Treatment2)) {
  data_t2 <- mlogit_df[mlogit_df$treatment == 2, ]
  market_shares$Treatment2 <- calculate_market_shares(table1_results$Treatment2, data_t2)
}

# 创建表3A：无条件市场份额
table3a <- data.frame(
  Group = character(),
  Category = character(),
  Percentage = character(),
  stringsAsFactors = FALSE
)

if (!is.null(market_shares$Treatment1)) {
  t1_shares <- market_shares$Treatment1
  for (i in 1:nrow(t1_shares)) {
    table3a <- rbind(table3a, data.frame(
      Group = "T1 - Control",
      Category = case_when(
        t1_shares$Option[i] == "A" ~ "Lab-grown",
        t1_shares$Option[i] == "B" ~ "Plant-based",
        t1_shares$Option[i] == "C" ~ "Beef"
      ),
      Percentage = sprintf("%.0f%%", t1_shares$x[i] * 100),
      stringsAsFactors = FALSE
    ))
  }
}

if (!is.null(market_shares$Treatment2)) {
  t2_shares <- market_shares$Treatment2
  for (i in 1:nrow(t2_shares)) {
    table3a <- rbind(table3a, data.frame(
      Group = "T2 - Information",
      Category = case_when(
        t2_shares$Option[i] == "A" ~ "Lab-grown",
        t2_shares$Option[i] == "B" ~ "Plant-based",
        t2_shares$Option[i] == "C" ~ "Beef"
      ),
      Percentage = sprintf("%.0f%%", t2_shares$x[i] * 100),
      stringsAsFactors = FALSE
    ))
  }
}

print("表3A：无条件市场份额")
print(table3a)
write.csv(table3a, "table3a_unconditional_market_shares.csv", row.names = FALSE)

# ===============================================================================
# 第九部分：人口统计数据与市场份额的关系
# ===============================================================================

cat("分析人口统计数据与市场份额的关系...\n")

# 提取人口统计变量（假设这些变量存在于原始数据中）
# demo_vars <- c("Age", "Gender", "Income", "Education", "Region")
# 修改为与你的数据匹配：
demo_vars <- c(
  "Q4. What is your age?",
  "Q5. What is your gender?",
  "Q36. Please indicate your approximate annual household income before taxes:",
  "Q31. What is your educational background? ",
  "Q6. Which of the following best describes your current residence?"
)

# 检查哪些变量实际存在
available_demo_vars <- demo_vars[demo_vars %in% names(raw)]

if (length(available_demo_vars) > 0) {
  cat("发现人口统计变量:", paste(available_demo_vars, collapse = ", "), "\n")

  # 合并人口统计数据到模型数据
  demo_data <- raw %>%
    select(respondent_id = UserNo, all_of(available_demo_vars))

  df_model_demo <- df_model_clean %>%
    left_join(demo_data, by = "respondent_id")

  # 创建包含人口统计变量的mlogit数据
  mlogit_df_demo <- mlogit.data(
    df_model_demo,
    choice = "choice",
    shape = "long",
    alt.var = "Option",
    chid.var = "chid",
    id.var = "respondent_id"
  )

  cat("包含人口统计变量的数据准备完成\n")
} else {
  cat("未发现标准人口统计变量，将使用模拟数据进行演示\n")

  # 创建模拟人口统计数据
  unique_ids <- unique(df_model_clean$respondent_id)
  demo_data <- data.frame(
    respondent_id = unique_ids,
    Age = sample(18:70, length(unique_ids), replace = TRUE),
    Gender = sample(c("Male", "Female"), length(unique_ids), replace = TRUE),
    Income = sample(c("Low", "Medium", "High"), length(unique_ids), replace = TRUE),
    Education = sample(c("High School", "College", "Graduate"), length(unique_ids), replace = TRUE),
    stringsAsFactors = FALSE
  )

  # 合并到模型数据
  df_model_demo <- df_model_clean %>%
    left_join(demo_data, by = "respondent_id")

  # 创建虚拟变量
  df_model_demo <- df_model_demo %>%
    mutate(
      Female = as.integer(Gender == "Female"),
      Income_Medium = as.integer(Income == "Medium"),
      Income_High = as.integer(Income == "High"),
      College = as.integer(Education == "College"),
      Graduate = as.integer(Education == "Graduate")
    )
}

cat("人口统计数据分析准备完成\n")

# ===============================================================================
# 第十部分：运行分析和生成报告
# ===============================================================================

cat("开始运行完整分析...\n")

# 创建输出目录
if (!dir.exists("results")) {
  dir.create("results")
}

# 生成综合报告
generate_report <- function() {
  cat("生成综合分析报告...\n")

  report <- c(
    "# 英国消费者对可持续肉类的支付意愿研究报告",
    "## Research: Preferences for sustainable meat choice in the UK",
    "",
    "### 研究目标",
    "1. 英国消费者对可持续肉类的支付意愿（WTP）",
    "2. 信息提供是否会影响英国消费者对可持续肉类的支付意愿",
    "3. 可持续肉类在英国消费者中的市场份额",
    "",
    "### 数据概况",
    paste("- 总样本量:", nrow(raw), "人"),
    paste("- 有效选择观测:", nrow(dce_long), "个"),
    paste("- Treatment 1 (Control):", sum(raw$treatment == 1, na.rm = TRUE), "人"),
    paste("- Treatment 2 (Information):", sum(raw$treatment == 2, na.rm = TRUE), "人"),
    "",
    "### 主要发现",
    "",
    "#### 表1：随机参数Logit模型估计结果",
    "- 模型成功估计了不同treatment组的偏好参数",
    "- 详细结果见 table1_mixed_logit_results.csv",
    "",
    "#### 表1(2)：各产品正面偏好比例",
    "- 计算了各肉类产品的正面偏好比例",
    "- 详细结果见 table1_2_positive_preferences.csv",
    "",
    "#### 表2：信息提供对支付意愿的影响",
    "- 比较了有无信息提供情况下的WTP差异",
    "- 详细结果见 table2_wtp_comparison.csv",
    "",
    "#### 表3：市场份额预测",
    "- 预测了不同情况下的市场份额",
    "- 详细结果见 table3a_unconditional_market_shares.csv",
    "",
    "### 结论",
    "1. Mixed logit模型成功捕捉了消费者偏好的异质性",
    "2. 信息提供对消费者的肉类选择偏好产生了影响",
    "3. 不同类型肉类产品的市场份额存在显著差异",
    "",
    "### 文件说明",
    "- mixed_logit_analysis.R: 主分析脚本",
    "- table1_mixed_logit_results.csv: 表1结果",
    "- table1_2_positive_preferences.csv: 表1(2)结果",
    "- table2_wtp_comparison.csv: 表2结果",
    "- table3a_unconditional_market_shares.csv: 表3A结果",
    "",
    paste("报告生成时间:", Sys.time())
  )

  writeLines(report, "results/analysis_report.md")
  cat("综合报告已保存到 results/analysis_report.md\n")
}

# 生成报告
generate_report()

# ===============================================================================
# 分析完成
# ===============================================================================

cat("\n", paste(rep("=", 80), collapse = ""), "\n")
cat("分析完成！\n")
cat(paste(rep("=", 80), collapse = ""), "\n")

cat("主要输出文件：\n")
cat("1. table1_mixed_logit_results.csv - 表1：随机参数Logit模型估计\n")
cat("2. table1_2_positive_preferences.csv - 表1(2)：正面偏好比例\n")
cat("3. table2_wtp_comparison.csv - 表2：信息提供对WTP的影响\n")
cat("4. table3a_unconditional_market_shares.csv - 表3A：市场份额\n")
cat("5. results/analysis_report.md - 综合分析报告\n")

cat("\n建议后续步骤：\n")
cat("1. 检查模型收敛性和统计显著性\n")
cat("2. 进行模型诊断和稳健性检验\n")
cat("3. 根据需要调整模型规格\n")
cat("4. 生成更详细的图表和可视化\n")

cat("\n分析脚本执行完毕！\n")
