# ===============================================================================
# 英国消费者对可持续肉类的支付意愿研究 - 简化版本
# ===============================================================================

# 加载必要的包
library(readxl)
library(dplyr)
library(tidyr)
library(stringr)
library(mlogit)

# 设置选项
options(digits = 4)
set.seed(123)

cat("开始数据分析...\n")

# ===============================================================================
# 第一部分：数据准备
# ===============================================================================

# 1. 读取数据
raw <- read_excel("data.xlsx", sheet = 1)
cat("原始数据维度:", dim(raw), "\n")

# 2. 处理标题行
title <- raw[1:2,]
raw <- raw[-c(1,2),]

# 3. 处理treatment变量
raw <- raw %>%
  rename(Q7 = starts_with("Q7. Cheap talk")) %>%
  mutate(Q7_trim = str_trim(Q7)) %>%
  mutate(treatment = case_when(
    is.na(Q7_trim) ~ NA_integer_,
    Q7_trim == "-" ~ 1L,  # Control group
    str_starts(Q7_trim, "Yes") ~ 2L,  # Treatment group
    TRUE ~ 1L
  ))

cat("Treatment分布:\n")
print(table(raw$treatment, useNA="ifany"))

# 4. 重命名选择题列
names(raw)[21:28] <- paste0("Q", 1:8, "_choice")

# 5. 创建简化的设计矩阵
design <- expand.grid(
  Task = 1:8,
  Option = c("A", "B", "C"),
  stringsAsFactors = FALSE
) %>%
  mutate(
    MeatType = case_when(
      Option == "A" ~ "Lab-grown",
      Option == "B" ~ "Plant-based", 
      Option == "C" ~ "Beef"
    ),
    Price = case_when(
      Option == "A" ~ 5,  # 固定价格简化分析
      Option == "B" ~ 4,
      Option == "C" ~ 6
    )
  )

# ===============================================================================
# 第二部分：数据转换
# ===============================================================================

# 1. 提取DCE数据
dce_raw <- raw %>%
  select(respondent_id = UserNo, treatment, Q1_choice:Q8_choice)

# 2. 转换为长格式
dce_long <- dce_raw %>%
  pivot_longer(
    cols = Q1_choice:Q8_choice,
    names_to = "qn",
    values_to = "chosen_Option"
  ) %>%
  mutate(task_id = as.integer(str_extract(qn, "\\d+"))) %>%
  select(respondent_id, task_id, chosen_Option, treatment)

# 3. 清理选择数据
dce_long <- dce_long %>%
  mutate(chosen_Option = str_extract(chosen_Option, "[ABC]")) %>%
  filter(!is.na(chosen_Option)) %>%
  select(respondent_id, task_id, chosen_Option, treatment)

cat("清洗后数据维度:", dim(dce_long), "\n")

# 4. 创建模型数据
resp_tasks <- dce_long %>%
  distinct(respondent_id, task_id, treatment)

options <- design %>% distinct(Option)
resp_task_options <- crossing(resp_tasks, options)

df_model <- resp_task_options %>%
  left_join(design, by = c("task_id" = "Task", "Option" = "Option"))

# 5. 添加选择标记
dce_flags <- dce_long %>%
  transmute(respondent_id, task_id, Option = chosen_Option, choice_flag = 1L)

df_model <- df_model %>%
  left_join(dce_flags, by = c("respondent_id","task_id","Option")) %>%
  mutate(choice = replace_na(choice_flag, 0L)) %>%
  select(-choice_flag)

# 6. 创建虚拟变量
df_model <- df_model %>%
  mutate(
    ASC_C = as.integer(Option == "C"),
    meat_Lab = as.integer(MeatType == "Lab-grown" & Option != "C"),
    meat_Plant = as.integer(MeatType == "Plant-based" & Option != "C"),
    chid = paste(respondent_id, task_id, sep = "_"),
    choice = choice == 1
  )

# 7. 创建mlogit数据
mlogit_df <- mlogit.data(
  df_model,
  choice = "choice",
  shape = "long",
  alt.var = "Option",
  chid.var = "chid",
  id.var = "respondent_id"
)

cat("mlogit数据准备完成\n")

# ===============================================================================
# 第三部分：基础条件Logit模型
# ===============================================================================

cat("估计基础条件Logit模型...\n")

# 整体模型 - 简化版本，不包含价格
model_pooled <- mlogit(
  choice ~ meat_Lab + meat_Plant | 1,
  data = mlogit_df
)

cat("整体模型结果:\n")
print(summary(model_pooled))

# 按treatment分组的模型
data_t1 <- mlogit_df[mlogit_df$treatment == 1, ]
data_t2 <- mlogit_df[mlogit_df$treatment == 2, ]

if(nrow(data_t1) > 0) {
  tryCatch({
    model_t1 <- mlogit(
      choice ~ meat_Lab + meat_Plant | 1,
      data = data_t1
    )
    cat("\nTreatment 1 (Control)模型结果:\n")
    print(summary(model_t1))
  }, error = function(e) {
    cat("\nTreatment 1模型估计失败:", e$message, "\n")
    model_t1 <- NULL
  })
}

if(nrow(data_t2) > 0) {
  tryCatch({
    model_t2 <- mlogit(
      choice ~ meat_Lab + meat_Plant | 1,
      data = data_t2
    )
    cat("\nTreatment 2 (Information)模型结果:\n")
    print(summary(model_t2))
  }, error = function(e) {
    cat("\nTreatment 2模型估计失败:", e$message, "\n")
    model_t2 <- NULL
  })
}

# ===============================================================================
# 第四部分：计算支付意愿
# ===============================================================================

cat("\n计算偏好系数...\n")

# 函数：提取偏好系数
extract_preferences <- function(model) {
  if(is.null(model)) return(NULL)

  coefs <- coef(model)

  pref_lab <- coefs["meat_Lab"]
  pref_plant <- coefs["meat_Plant"]

  return(c(Lab_grown = pref_lab, Plant_based = pref_plant))
}

# 计算各组偏好系数
pref_pooled <- extract_preferences(model_pooled)
cat("整体偏好系数:\n")
print(pref_pooled)

if(exists("model_t1") && !is.null(model_t1)) {
  pref_t1 <- extract_preferences(model_t1)
  cat("\nTreatment 1 偏好系数:\n")
  print(pref_t1)
}

if(exists("model_t2") && !is.null(model_t2)) {
  pref_t2 <- extract_preferences(model_t2)
  cat("\nTreatment 2 偏好系数:\n")
  print(pref_t2)
}

# ===============================================================================
# 第五部分：市场份额预测
# ===============================================================================

cat("\n计算市场份额...\n")

# 预测选择概率
pred_pooled <- predict(model_pooled, newdata = mlogit_df)
market_shares_pooled <- aggregate(pred_pooled, 
                                 by = list(Option = mlogit_df$Option), 
                                 FUN = mean)

cat("整体市场份额:\n")
print(market_shares_pooled)

# ===============================================================================
# 第六部分：保存结果
# ===============================================================================

cat("\n保存结果...\n")

# 创建结果汇总
results_summary <- data.frame(
  Model = c("Pooled", "Treatment1", "Treatment2"),
  Lab_Preference = c(
    ifelse(exists("pref_pooled"), pref_pooled["Lab_grown"], NA),
    ifelse(exists("pref_t1") && !is.null(pref_t1), pref_t1["Lab_grown"], NA),
    ifelse(exists("pref_t2") && !is.null(pref_t2), pref_t2["Lab_grown"], NA)
  ),
  Plant_Preference = c(
    ifelse(exists("pref_pooled"), pref_pooled["Plant_based"], NA),
    ifelse(exists("pref_t1") && !is.null(pref_t1), pref_t1["Plant_based"], NA),
    ifelse(exists("pref_t2") && !is.null(pref_t2), pref_t2["Plant_based"], NA)
  ),
  stringsAsFactors = FALSE
)

write.csv(results_summary, "preference_results.csv", row.names = FALSE)
write.csv(market_shares_pooled, "market_shares.csv", row.names = FALSE)

# 创建选择频率统计
choice_freq <- dce_long %>%
  group_by(treatment, chosen_Option) %>%
  summarise(count = n(), .groups = "drop") %>%
  group_by(treatment) %>%
  mutate(percentage = count / sum(count) * 100)

write.csv(choice_freq, "choice_frequencies.csv", row.names = FALSE)

cat("分析完成！结果已保存到:\n")
cat("- preference_results.csv: 偏好系数结果\n")
cat("- market_shares.csv: 市场份额预测\n")
cat("- choice_frequencies.csv: 选择频率统计\n")

# 显示选择频率
cat("\n选择频率统计:\n")
print(choice_freq)
