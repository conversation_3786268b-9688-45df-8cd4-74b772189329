# ===============================================================================
# 创建简单可视化图表
# ===============================================================================

library(ggplot2)
library(dplyr)
library(tidyr)

cat("开始创建可视化图表...\n")

# 读取数据
market_shares <- read.csv("table3_market_shares_basic.csv")
choice_freq <- read.csv("detailed_choice_frequencies.csv")

# 设置图表主题
theme_set(theme_minimal() + 
  theme(
    plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
    axis.text = element_text(size = 10),
    axis.title = element_text(size = 12),
    legend.title = element_text(size = 11),
    legend.text = element_text(size = 10)
  ))

# ===============================================================================
# 图1：市场份额对比
# ===============================================================================

# 准备数据
market_data <- market_shares %>%
  select(Product, Treatment_1, Treatment_2) %>%
  pivot_longer(cols = c(Treatment_1, Treatment_2), 
               names_to = "Treatment", 
               values_to = "Share") %>%
  mutate(
    Treatment = case_when(
      Treatment == "Treatment_1" ~ "Control",
      Treatment == "Treatment_2" ~ "Information"
    ),
    Product = factor(Product, levels = c("Lab-grown meat", "Plant-based meat", "Conventional beef"))
  )

# 创建市场份额对比图
p1 <- ggplot(market_data, aes(x = Product, y = Share, fill = Treatment)) +
  geom_col(position = "dodge", alpha = 0.8) +
  geom_text(aes(label = paste0(round(Share, 1), "%")), 
            position = position_dodge(width = 0.9), 
            vjust = -0.5, size = 3.5) +
  scale_fill_manual(values = c("Control" = "#2E86AB", "Information" = "#A23B72")) +
  labs(
    title = "Market Shares by Treatment Group",
    subtitle = "Comparison of meat product preferences",
    x = "Product Type",
    y = "Market Share (%)",
    fill = "Treatment Group"
  ) +
  theme(axis.text.x = element_text(angle = 45, hjust = 1)) +
  ylim(0, 45)

ggsave("figure1_market_shares.png", p1, width = 10, height = 6, dpi = 300)
cat("图1已保存: figure1_market_shares.png\n")

# ===============================================================================
# 图2：信息提供的影响
# ===============================================================================

# 计算差异
differences <- market_shares %>%
  mutate(Difference = Treatment_2 - Treatment_1) %>%
  select(Product, Difference) %>%
  mutate(
    Direction = ifelse(Difference > 0, "Increase", "Decrease"),
    Product = factor(Product, levels = c("Lab-grown meat", "Plant-based meat", "Conventional beef"))
  )

# 创建差异图
p2 <- ggplot(differences, aes(x = Product, y = Difference, fill = Direction)) +
  geom_col(alpha = 0.8) +
  geom_text(aes(label = paste0(ifelse(Difference > 0, "+", ""), round(Difference, 1), "pp")), 
            vjust = ifelse(differences$Difference > 0, -0.5, 1.5), size = 4) +
  scale_fill_manual(values = c("Increase" = "#27AE60", "Decrease" = "#E74C3C")) +
  geom_hline(yintercept = 0, linetype = "dashed", alpha = 0.5) +
  labs(
    title = "Impact of Information Provision",
    subtitle = "Change in market share (percentage points)",
    x = "Product Type",
    y = "Change in Market Share (pp)",
    fill = "Direction"
  ) +
  theme(axis.text.x = element_text(angle = 45, hjust = 1))

ggsave("figure2_information_impact.png", p2, width = 10, height = 6, dpi = 300)
cat("图2已保存: figure2_information_impact.png\n")

# ===============================================================================
# 图3：替代肉类总偏好
# ===============================================================================

# 计算替代肉类总偏好
choice_data <- choice_freq %>%
  mutate(
    Treatment_Name = case_when(
      treatment == 1 ~ "Control",
      treatment == 2 ~ "Information"
    )
  )

alternative_pref <- choice_data %>%
  filter(chosen_Option != "C") %>%
  group_by(Treatment_Name) %>%
  summarise(Total_Alternative = sum(percentage), .groups = "drop")

# 创建替代肉类偏好图
p3 <- ggplot(alternative_pref, aes(x = Treatment_Name, y = Total_Alternative, fill = Treatment_Name)) +
  geom_col(alpha = 0.8, width = 0.6) +
  geom_text(aes(label = paste0(round(Total_Alternative, 1), "%")), 
            vjust = -0.5, size = 5) +
  scale_fill_manual(values = c("Control" = "#2E86AB", "Information" = "#A23B72")) +
  labs(
    title = "Total Preference for Alternative Meat Products",
    subtitle = "Combined preference for Lab-grown and Plant-based meat",
    x = "Treatment Group",
    y = "Total Alternative Meat Preference (%)",
    fill = "Treatment Group"
  ) +
  ylim(0, 80) +
  theme(legend.position = "none")

ggsave("figure3_alternative_preference.png", p3, width = 8, height = 6, dpi = 300)
cat("图3已保存: figure3_alternative_preference.png\n")

# ===============================================================================
# 图4：各产品选择频率对比
# ===============================================================================

# 创建选择频率图
choice_plot_data <- choice_data %>%
  mutate(
    Product_Name = case_when(
      chosen_Option == "A" ~ "Lab-grown meat",
      chosen_Option == "B" ~ "Plant-based meat",
      chosen_Option == "C" ~ "Conventional beef"
    ),
    Product_Name = factor(Product_Name, levels = c("Lab-grown meat", "Plant-based meat", "Conventional beef"))
  )

p4 <- ggplot(choice_plot_data, aes(x = Product_Name, y = percentage, fill = Treatment_Name)) +
  geom_col(position = "dodge", alpha = 0.8) +
  geom_text(aes(label = paste0(round(percentage, 1), "%")), 
            position = position_dodge(width = 0.9), 
            vjust = -0.5, size = 3.5) +
  scale_fill_manual(values = c("Control" = "#2E86AB", "Information" = "#A23B72")) +
  labs(
    title = "Choice Frequencies by Treatment Group",
    subtitle = "Percentage of choices for each product type",
    x = "Product Type",
    y = "Choice Frequency (%)",
    fill = "Treatment Group"
  ) +
  theme(axis.text.x = element_text(angle = 45, hjust = 1)) +
  ylim(0, 45)

ggsave("figure4_choice_frequencies.png", p4, width = 10, height = 6, dpi = 300)
cat("图4已保存: figure4_choice_frequencies.png\n")

# ===============================================================================
# 创建数据汇总表
# ===============================================================================

# 创建主要发现汇总
key_findings <- data.frame(
  Metric = c(
    "Total Sample Size",
    "Control Group Size", 
    "Information Group Size",
    "Total Choice Observations",
    "Control: Alternative Meat Preference",
    "Information: Alternative Meat Preference", 
    "Information Effect (pp)",
    "Lab-grown Market Share (Control)",
    "Lab-grown Market Share (Information)",
    "Plant-based Market Share (Control)",
    "Plant-based Market Share (Information)",
    "Conventional Beef Market Share (Control)",
    "Conventional Beef Market Share (Information)"
  ),
  Value = c(
    "587",
    "450 (76.7%)",
    "137 (23.3%)", 
    "3,617",
    paste0(round(alternative_pref$Total_Alternative[alternative_pref$Treatment_Name == "Control"], 1), "%"),
    paste0(round(alternative_pref$Total_Alternative[alternative_pref$Treatment_Name == "Information"], 1), "%"),
    paste0("+", round(alternative_pref$Total_Alternative[alternative_pref$Treatment_Name == "Information"] - 
                     alternative_pref$Total_Alternative[alternative_pref$Treatment_Name == "Control"], 1)),
    paste0(round(market_shares$Treatment_1[market_shares$Product == "Lab-grown meat"], 1), "%"),
    paste0(round(market_shares$Treatment_2[market_shares$Product == "Lab-grown meat"], 1), "%"),
    paste0(round(market_shares$Treatment_1[market_shares$Product == "Plant-based meat"], 1), "%"), 
    paste0(round(market_shares$Treatment_2[market_shares$Product == "Plant-based meat"], 1), "%"),
    paste0(round(market_shares$Treatment_1[market_shares$Product == "Conventional beef"], 1), "%"),
    paste0(round(market_shares$Treatment_2[market_shares$Product == "Conventional beef"], 1), "%")
  ),
  stringsAsFactors = FALSE
)

write.csv(key_findings, "key_findings_summary.csv", row.names = FALSE)

cat("\n可视化图表创建完成！\n")
cat("生成的图表文件：\n")
cat("- figure1_market_shares.png: 市场份额对比\n")
cat("- figure2_information_impact.png: 信息提供的影响\n")
cat("- figure3_alternative_preference.png: 替代肉类总偏好\n")
cat("- figure4_choice_frequencies.png: 选择频率分布\n")
cat("- key_findings_summary.csv: 主要发现汇总\n")

# 显示主要发现
cat("\n=== 主要发现汇总 ===\n")
print(key_findings)
